"""
Web Search Module for Agriculture-related queries.
Uses DuckDuckGo search API to find information when RAG system doesn't have relevant content.
"""

import logging
import re
from typing import Dict, Any, List, Optional
from ddgs import DDGS

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class AgricultureWebSearch:
    """
    Web search functionality specifically for agriculture-related queries.
    """
    
    def __init__(self):
        """Initialize the web search client."""
        self.ddgs = DDGS()
        
        # Agriculture-related keywords for topic detection
        self.agriculture_keywords = {
            # Core agriculture terms
            'agriculture', 'farming', 'crop', 'crops', 'cultivation', 'harvest', 'harvesting',
            'soil', 'fertilizer', 'fertilizers', 'pesticide', 'pesticides', 'irrigation',
            'seeds', 'planting', 'sowing', 'tillage', 'livestock', 'cattle', 'dairy',
            'poultry', 'chicken', 'pig', 'sheep', 'goat', 'farm', 'farmer', 'farmers',
            
            # Plant science terms
            'botany', 'plant', 'plants', 'vegetation', 'photosynthesis', 'germination',
            'pollination', 'pruning', 'grafting', 'horticulture', 'floriculture',
            'viticulture', 'silviculture', 'forestry', 'agroforestry',
            
            # Crop types
            'wheat', 'rice', 'corn', 'maize', 'barley', 'oats', 'soybean', 'cotton',
            'sugarcane', 'potato', 'tomato', 'vegetables', 'fruits', 'cereals', 'grains',
            'legumes', 'pulses', 'spices', 'herbs', 'tea', 'coffee', 'cocoa',
            
            # Agricultural sciences
            'agronomy', 'agrology', 'pedology', 'entomology', 'pathology', 'breeding',
            'genetics', 'biotechnology', 'hydroponics', 'aquaponics', 'organic',
            'sustainable', 'precision', 'mechanization', 'automation',
            
            # Agricultural economics and policy
            'agricultural economics', 'farm management', 'agricultural policy',
            'food security', 'rural development', 'cooperative', 'extension',
            
            # Environmental agriculture
            'climate change', 'drought', 'flood', 'weather', 'season', 'monsoon',
            'greenhouse', 'conservation', 'biodiversity', 'ecosystem',
            
            # Agricultural technology
            'tractor', 'machinery', 'equipment', 'tools', 'technology', 'innovation',
            'sensors', 'drones', 'satellite', 'gps', 'precision farming',
            
            # Food and nutrition
            'food', 'nutrition', 'processing', 'storage', 'preservation', 'quality',
            'safety', 'supply chain', 'market', 'trade'
        }
        
        # Additional context keywords that might indicate agriculture
        self.context_keywords = {
            'university', 'college', 'research', 'study', 'degree', 'course', 'program',
            'department', 'faculty', 'education', 'training', 'extension', 'development'
        }
    
    def is_agriculture_related(self, query: str) -> bool:
        """
        Determine if a query is agriculture-related.
        
        Args:
            query (str): User query to analyze
            
        Returns:
            bool: True if query is agriculture-related, False otherwise
        """
        query_lower = query.lower()
        
        # Remove common stop words and punctuation for better matching
        cleaned_query = re.sub(r'[^\w\s]', ' ', query_lower)
        query_words = set(cleaned_query.split())
        
        # Check for direct agriculture keyword matches
        agriculture_matches = len(query_words.intersection(self.agriculture_keywords))
        context_matches = len(query_words.intersection(self.context_keywords))
        
        # Consider it agriculture-related if:
        # 1. Has at least one direct agriculture keyword, OR
        # 2. Has agriculture context keywords AND query mentions common agriculture terms
        if agriculture_matches >= 1:
            logger.info(f"Query identified as agriculture-related: {agriculture_matches} direct matches")
            return True
        
        # Check for agriculture context with educational terms
        if context_matches >= 1 and any(term in query_lower for term in ['agricultural', 'farm', 'crop', 'soil', 'plant']):
            logger.info(f"Query identified as agriculture-related: context + agriculture terms")
            return True
        
        # Check for common agriculture phrases and patterns
        agriculture_phrases = [
            'what is agriculture', 'types of farming', 'agricultural practices',
            'crop production', 'plant growth', 'soil management', 'farm management',
            'agricultural technology', 'sustainable farming', 'organic farming',
            'how to grow', 'how to plant', 'how to cultivate', 'growing tips',
            'planting guide', 'farming methods', 'cultivation techniques'
        ]

        if any(phrase in query_lower for phrase in agriculture_phrases):
            logger.info(f"Query identified as agriculture-related: phrase match")
            return True

        # Check for "how to grow/plant/cultivate" + plant/crop names
        grow_patterns = [
            r'how to (grow|plant|cultivate|raise)\s+\w+',
            r'growing\s+\w+',
            r'planting\s+\w+',
            r'cultivating\s+\w+'
        ]

        for pattern in grow_patterns:
            if re.search(pattern, query_lower):
                logger.info(f"Query identified as agriculture-related: grow/plant pattern match")
                return True
        
        logger.info(f"Query not identified as agriculture-related")
        return False
    
    def search_agriculture_info(self, query: str, max_results: int = 5) -> Dict[str, Any]:
        """
        Search for agriculture-related information using DuckDuckGo.
        
        Args:
            query (str): Search query
            max_results (int): Maximum number of search results to return
            
        Returns:
            Dict[str, Any]: Search results with formatted content
        """
        try:
            logger.info(f"Performing web search for agriculture query: '{query}'")
            
            # Enhance query with agriculture context for better results
            enhanced_query = f"{query} agriculture farming"
            
            # Perform search
            search_results = list(self.ddgs.text(
                enhanced_query,
                max_results=max_results,
                safesearch='moderate',
                timelimit='y'  # Results from past year for more current information
            ))
            
            if not search_results:
                logger.warning("No search results found")
                return {
                    'success': False,
                    'error': 'No search results found',
                    'results': [],
                    'formatted_content': ''
                }
            
            # Format results for LLM consumption
            formatted_content = self._format_search_results(search_results, query)
            
            logger.info(f"Successfully retrieved {len(search_results)} search results")
            
            return {
                'success': True,
                'results': search_results,
                'formatted_content': formatted_content,
                'num_results': len(search_results)
            }
            
        except Exception as e:
            logger.error(f"Error during web search: {e}")
            return {
                'success': False,
                'error': str(e),
                'results': [],
                'formatted_content': ''
            }
    
    def _format_search_results(self, results: List[Dict], query: str) -> str:
        """
        Format search results into a coherent context for the LLM.
        
        Args:
            results (List[Dict]): Raw search results from DuckDuckGo
            query (str): Original user query
            
        Returns:
            str: Formatted content for LLM context
        """
        formatted_parts = []
        
        formatted_parts.append(f"Web search results for agriculture query: '{query}'\n")
        formatted_parts.append("=" * 60 + "\n")
        
        for i, result in enumerate(results, 1):
            title = result.get('title', 'No title')
            body = result.get('body', 'No description')
            href = result.get('href', 'No URL')
            
            # Clean and truncate content
            body = body.strip()
            if len(body) > 300:
                body = body[:300] + "..."
            
            formatted_parts.append(f"\nResult {i}:")
            formatted_parts.append(f"Title: {title}")
            formatted_parts.append(f"Content: {body}")
            formatted_parts.append(f"Source: {href}")
            formatted_parts.append("-" * 40)
        
        formatted_parts.append(f"\nNote: This information was retrieved from web search to supplement the knowledge base.")
        
        return "\n".join(formatted_parts)
    
    def get_search_stats(self) -> Dict[str, Any]:
        """
        Get statistics about the web search functionality.
        
        Returns:
            Dict[str, Any]: Search statistics and configuration
        """
        return {
            'agriculture_keywords_count': len(self.agriculture_keywords),
            'context_keywords_count': len(self.context_keywords),
            'search_engine': 'DuckDuckGo',
            'max_results_default': 5,
            'safesearch': 'moderate',
            'time_limit': 'past year'
        }


def main():
    """
    Test function for the web search module.
    """
    try:
        # Initialize web search
        web_search = AgricultureWebSearch()
        
        # Test queries
        test_queries = [
            "What is agriculture?",
            "Types of farming methods",
            "How to grow tomatoes?",
            "What is the weather today?",  # Non-agriculture query
            "Sustainable farming practices",
            "Agricultural technology innovations"
        ]
        
        print("Testing Agriculture Web Search Module")
        print("=" * 50)
        
        for query in test_queries:
            print(f"\nQuery: {query}")
            is_agriculture = web_search.is_agriculture_related(query)
            print(f"Agriculture-related: {is_agriculture}")
            
            if is_agriculture:
                results = web_search.search_agriculture_info(query, max_results=2)
                if results['success']:
                    print(f"Search successful: {results['num_results']} results")
                    print("Sample content:")
                    print(results['formatted_content'][:200] + "...")
                else:
                    print(f"Search failed: {results['error']}")
            
            print("-" * 30)
        
        # Print stats
        stats = web_search.get_search_stats()
        print(f"\nSearch Statistics: {stats}")
        
    except Exception as e:
        print(f"Error testing web search module: {e}")


if __name__ == "__main__":
    main()
